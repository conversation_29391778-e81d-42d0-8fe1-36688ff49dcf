import React from 'react';
import { Container } from '@ghq-abi/design-system-v2';

import { TargetCard } from '~/shared/components/TargetCard';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import { TabEmptyState } from '../TabEmptyState';

interface FinalStepProps {
  targets: Target[];
  proposalStatus?: ProposalStatusEnum;
}

export function FinalStep({ targets, proposalStatus }: FinalStepProps) {
  const finalTargets = targets.filter(target =>
    target.targetTypes?.some(
      targetType => targetType.type === TargetTypeEnum.FINAL,
    ),
  );

  if (
    proposalStatus === ProposalStatusEnum.IN_PROGRESS_PROPOSAL ||
    proposalStatus === ProposalStatusEnum.IN_PROGRESS_FEEDBACK ||
    finalTargets.length === 0
  ) {
    return (
      <TabEmptyState
        title="Nothing to see here yet"
        description="This page will be available for completion after the employee submits feedback."
      />
    );
  }

  return (
    <>
      <Container className="flex flex-col gap-4">
        {finalTargets.map(target => (
          <TargetCard
            key={target.uid}
            data={target}
            proposalStatus={proposalStatus}
            currentTargetType={TargetTypeEnum.FINAL}
          />
        ))}
      </Container>
    </>
  );
}
