import { useMemo, useState } from 'react';
import { useMutation } from '@tanstack/react-query';
import { useTranslate } from '@tolgee/react';

import { useCatalog } from '~/entities/Home/hooks/useCatalog';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import proposalService from '~/shared/services/proposal';
import targetTypesService from '~/shared/services/targetTypes';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Proposal } from '~/shared/types/Proposal';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

import {
  CreateTargetBody,
  UseFeedbackParams,
  UseFeedbackReturn,
} from './types';

export function useFeedback({
  proposalUid,
  targets,
  allTargets,
  catalogFilterHook,
}: UseFeedbackParams): UseFeedbackReturn {
  const { t } = useTranslate();
  const actionModal = useActionModal();

  // States
  const [draggedItem, setDraggedItem] = useState<
    DeliverableItem | Target | null
  >(null);
  const [selectedTargets, setSelectedTargets] = useState<Target[]>(
    targets || [],
  );
  const [acceptedTargetUids, setAcceptedTargetUids] = useState<string[]>([]);
  const [isDraggingNewItem, setIsDraggingNewItem] = useState(false);
  const [isOpenDrawer, setIsOpenDrawer] = useState<boolean>(false);
  const [drawerDeliverable, setDrawerDeliverable] = useState<DeliverableItem>();

  const { filters } = catalogFilterHook;

  const {
    data: deliverables,
    isSearchLoading,
    isInitialLoading,
    isError,
  } = useCatalog(1, 100, undefined, filters);

  // Mutations
  const { isLoading, mutate: mutateFinal } = useMutation({
    mutationFn: () => {
      return proposalService.changeProposalStatus(
        proposalUid,
        ProposalStatusEnum.IN_PROGRESS_FINAL,
      );
    },
    onSuccess: (response: Proposal) => {
      // Proposal sent to final successfully
    },
    onError: error => {
      console.error(error);
    },
  });

  const { isLoading: isLoadingDelete, mutate: mutateDelete } = useMutation({
    mutationFn: (targets: Target[]) => {
      return proposalService.deleteTargets(
        proposalUid,
        targets.map(t => t.uid || ''),
      );
    },
    onSuccess: (proposal: Proposal) => {
      const feedbackTargets = (proposal.targets || []).filter(target =>
        target.targetTypes?.some(
          targetType => targetType.type === TargetTypeEnum.FEEDBACK,
        ),
      );
      setSelectedTargets(feedbackTargets);
      actionModal.closeModal();
    },
    onError: error => {
      console.error(error);
    },
  });

  const getChildForBodyRequest = (child: Target[]) => {
    return child.map(c => ({
      weight: c.weight,
      scope: c.scope,
      uidDeliverable: c.deliverable?.uid || '',
      ...(c.uid && { uid: c.uid }),
    }));
  };

  const { isLoading: isLoadingMergeTargets, mutate: mutateMergeTargets } =
    useMutation({
      mutationFn: (values: { firstTarget: Target; secondTarget: Target }) => {
        const firstTarget = values.firstTarget as Target;
        const secondTarget = values.secondTarget as Target;

        const treatedChildren = [];

        // Handle first target - can have children or be a simple target
        if (firstTarget.children && firstTarget.children.length > 0) {
          treatedChildren.push(...getChildForBodyRequest(firstTarget.children));
        } else {
          treatedChildren.push({
            weight: firstTarget.weight,
            scope: firstTarget.scope,
            uidDeliverable:
              firstTarget.uidDeliverable || firstTarget.deliverable?.uid || '',
            ...(firstTarget.uid && { uid: firstTarget.uid }),
          });
        }

        // Handle second target - can have children or be a simple target
        if (secondTarget.children && secondTarget.children.length > 0) {
          treatedChildren.push(
            ...getChildForBodyRequest(secondTarget.children),
          );
        } else {
          treatedChildren.push({
            weight: secondTarget.weight,
            scope: secondTarget.scope,
            uidDeliverable:
              secondTarget.uidDeliverable ||
              secondTarget.deliverable?.uid ||
              '',
            ...(secondTarget.uid && { uid: secondTarget.uid }),
          });
        }

        const data: CreateTargetBody = {
          targets: [
            {
              weight: (firstTarget.weight || 0) + (secondTarget.weight || 0),
              targetType: TargetTypeEnum.FEEDBACK,
              children: treatedChildren,
            },
          ],
        };

        return proposalService.createTarget(proposalUid, data);
      },
      onSuccess: (proposal: Proposal) => {
        const feedbackTargets = (proposal.targets || []).filter(target =>
          target.targetTypes?.some(
            targetType => targetType.type === TargetTypeEnum.FEEDBACK,
          ),
        );
        setSelectedTargets(feedbackTargets);
        actionModal.closeModal();
      },
      onError: error => {
        console.error(error);
      },
    });

  const { isLoading: isLoadingAcceptTargets, mutate: mutateAcceptTargets } =
    useMutation({
      mutationFn: (targetUids: string[]) => {
        return targetTypesService.addFeedbackToTargets(targetUids);
      },
      onSuccess: response => {},
      onError: error => {
        console.error('Error accepting targets:', error);
      },
    });

  // Computed values
  const availableDeliverables = useMemo(() => {
    const selectedUids = new Set(
      selectedTargets.flatMap(d => {
        if (d.children && d.children.length > 0) {
          return d.children
            .map(child => child.deliverable?.uid)
            .filter(Boolean);
        } else {
          return d.deliverable?.uid ? [d.deliverable.uid] : [];
        }
      }),
    );

    const deliverableItems = deliverables?.data || [];
    return deliverableItems.filter(d => !selectedUids.has(d.uid));
  }, [deliverables, selectedTargets]);

  // Handlers
  const handleAcceptTarget = (targetUid: string) => {
    if (!acceptedTargetUids.includes(targetUid)) {
      const targetToAccept = allTargets.find(
        target => target.uid === targetUid,
      );

      if (targetToAccept) {
        mutateAcceptTargets([targetUid]);

        const feedbackTarget = {
          ...targetToAccept,
          targetTypes: [
            ...(targetToAccept.targetTypes || []),
            { type: TargetTypeEnum.FEEDBACK },
          ],
        };

        setAcceptedTargetUids(prev => [...prev, targetUid]);
        setSelectedTargets(prev => [...prev, feedbackTarget]);
      }
    }
  };

  const handleDragStart = (event: any) => {
    const item = event.active.data.current?.data;

    if (item) {
      setDraggedItem(item);
      const isFromSideList = availableDeliverables.some(
        d => d.uid === item.uid,
      );
      setIsDraggingNewItem(isFromSideList);
    }
  };

  const handleDragEnd = (event: any) => {
    const { active, over } = event;

    if (over && over.id === 'selection-area') {
      const deliverable = active.data.current?.data;
      if (
        deliverable &&
        !selectedTargets.find(d => d.uid === deliverable.uid)
      ) {
        setDrawerDeliverable(deliverable);
        setIsOpenDrawer(true);
      }
    } else if (over && over.id.toString().includes('target')) {
      const firstTarget = over.data.current?.target as Target;
      const secondTarget = active.data.current?.data as Target;

      if (firstTarget && secondTarget && firstTarget.uid !== secondTarget.uid) {
        actionModal.openModal({
          title: t('common_merge_targets'),
          message: t('common_do_you_really_want_to_merge_the_targets'),
          actions: [
            {
              label: t('common_yes'),
              onClick: () => {},
            },
            {
              label: t('common_no'),
              onClick: actionModal.closeModal,
              variant: 'secondary',
            },
          ],
          onConfirm: async () => {
            const values = { firstTarget, secondTarget };
            await mutateMergeTargets(values);
          },
        });
      }
    }

    setDraggedItem(null);
    setIsDraggingNewItem(false);
  };

  const onDrawerSuccessSubmit = (proposal: Proposal) => {
    const feedbackTargets = (proposal.targets || []).filter(target =>
      target.targetTypes?.some(
        targetType => targetType.type === TargetTypeEnum.FEEDBACK,
      ),
    );
    setSelectedTargets(feedbackTargets);
  };

  const handleSubmit = () => {
    mutateFinal();
  };

  const handleClearDeliverable = () => {
    actionModal.openModal({
      title: t('common_clear_list'),
      message: t('common_do_you_really_want_to_clear_the_list'),
      actions: [
        {
          label: t('common_yes'),
          onClick: () => {},
        },
        {
          label: t('common_no'),
          onClick: actionModal.closeModal,
          variant: 'secondary',
        },
      ],
      onConfirm: async () => {
        if (isLoadingDelete) {
          return;
        }
        await mutateDelete(selectedTargets);
      },
    });
  };

  const handleTargetRemove = (targetId: string) => {
    setSelectedTargets(prev => prev.filter(t => t.uid !== targetId));
  };

  return {
    // States
    selectedTargets,
    acceptedTargetUids,
    draggedItem,
    isDraggingNewItem,
    isOpenDrawer,
    drawerDeliverable,

    // Loading states
    isLoading,
    isLoadingDelete,
    isLoadingMergeTargets,
    isLoadingAcceptTargets,

    // Handlers
    handleAcceptTarget,
    handleDragStart,
    handleDragEnd,
    handleSubmit,
    handleClearDeliverable,
    handleTargetRemove,
    onDrawerSuccessSubmit,
    setIsOpenDrawer,
    setDrawerDeliverable,

    // Computed values
    availableDeliverables,

    // Modal control
    actionModal,
  };
}
