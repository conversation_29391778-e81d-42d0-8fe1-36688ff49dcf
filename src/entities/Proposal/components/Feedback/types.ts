import { useCatalogFilter } from '~/entities/Home/hooks/useCatalogFilter';
import { useActionModal } from '~/shared/components/ActionModal/useActionModal';
import { DeliverableItem } from '~/shared/types/Deliverable';
import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';

export interface FeedbackDragProps {
  proposalStatus?: ProposalStatusEnum;
  proposalUid: string;
  targets: Target[];
  allTargets?: Target[];
}

export interface FeedbackStepProps {
  targets: Target[];
  proposalUid: string;
  proposalStatus?: ProposalStatusEnum;
  hasManagerPermission: boolean;
  hasEmployeePermission: boolean;
}

export interface CatalogWithTabsProps {
  deliverables: DeliverableItem[];
  proposalTargets: Target[];
  isInitialLoading: boolean;
  isSearchLoading: boolean;
  isError: boolean;
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
  onAcceptTarget?: (targetUid: string) => void;
  acceptedTargetUids?: string[];
}

export interface CreateTargetBody {
  targets: Target[];
}

export interface UseFeedbackReturn {
  // States
  selectedTargets: Target[];
  acceptedTargetUids: string[];
  draggedItem: DeliverableItem | Target | null;
  isDraggingNewItem: boolean;
  isOpenDrawer: boolean;
  drawerDeliverable: DeliverableItem | undefined;

  // Loading states
  isLoading: boolean;
  isLoadingDelete: boolean;
  isLoadingMergeTargets: boolean;
  isLoadingAcceptTargets: boolean;

  // Handlers
  handleAcceptTarget: (targetUid: string) => void;
  handleDragStart: (event: any) => void;
  handleDragEnd: (event: any) => void;
  handleSubmit: () => void;
  handleClearDeliverable: () => void;
  handleTargetRemove: (targetId: string) => void;
  onDrawerSuccessSubmit: (proposal: any) => void;
  setIsOpenDrawer: (open: boolean) => void;
  setDrawerDeliverable: (deliverable: DeliverableItem | undefined) => void;

  // Computed values
  availableDeliverables: DeliverableItem[];

  // Modal control
  actionModal: ReturnType<typeof useActionModal>;
}

export interface UseFeedbackParams {
  proposalUid: string;
  targets: Target[];
  allTargets: Target[];
  catalogFilterHook: ReturnType<typeof useCatalogFilter>;
}
