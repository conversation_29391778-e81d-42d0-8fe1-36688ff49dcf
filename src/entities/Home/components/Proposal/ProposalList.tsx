import { Container, Skeleton, Typography } from '@ghq-abi/design-system-v2';
import { useTranslate } from '@tolgee/react';

import { useProposal } from '~/entities/Home/hooks/useProposal';
import { NoResults, Pagination } from '~/shared/components';
import { usePagination } from '~/shared/hooks';
import { ProposalItemsResponse } from '~/shared/types/ProposalService';

import { useProposalFilter } from '../../hooks/useProposalFilter';

import { ProposalCard } from './ProposalCard';
import { ProposalFilter } from './ProposalFilter';

export const ProposalList = ({
  initialProposals,
}: {
  initialProposals?: ProposalItemsResponse;
}) => {
  const { t } = useTranslate();
  const proposalFilterHook = useProposalFilter();
  const { filters } = proposalFilterHook;

  const { totalPages, currentPage, setPageNumber, pageSize } = usePagination({
    initialPageSize: 9,
    totalRecords: 0,
  });

  const { proposals, isInitialLoading, isSearchLoading, isError } = useProposal(
    currentPage,
    pageSize,
    initialProposals,
    filters,
  );

  const actualTotalPages = proposals?.totalRecords
    ? Math.ceil(proposals.totalRecords / pageSize)
    : totalPages;

  return (
    <Container className="flex flex-col gap-4">
      <ProposalFilter {...proposalFilterHook} />
      {isError && (
        <Container className="flex justify-center items-center py-8">
          <Typography variant="metadata-sm-medium">
            {t('common_something_went_wrong')}
          </Typography>
        </Container>
      )}
      <Container className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {isInitialLoading || isSearchLoading
          ? Array.from({ length: 10 }).map((_, index) => (
              <Skeleton key={index} className="w-full h-[168px]" />
            ))
          : proposals?.data &&
            proposals?.data.length > 0 &&
            proposals?.data.map(proposal => (
              <ProposalCard
                key={proposal.uid}
                data={proposal}
                showActions
                showDrawerAction
                showDetailAction
              />
            ))}
      </Container>
      {!isInitialLoading &&
        !isSearchLoading &&
        proposals?.data &&
        proposals?.data.length === 0 && (
          <Container className="flex justify-center items-center py-8">
            <NoResults />
          </Container>
        )}
      <Container className="flex justify-center py-4 mt-8">
        <Pagination
          currentPage={currentPage}
          totalPages={actualTotalPages}
          onPageChange={setPageNumber}
        />
      </Container>
    </Container>
  );
};
