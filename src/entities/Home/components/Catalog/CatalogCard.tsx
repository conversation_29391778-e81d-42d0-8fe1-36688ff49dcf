import React from 'react';
import {
  ArrowsAngleExpand,
  ArrowUpRightSquare,
  BarChartFill,
  EyeSlash,
  ListCheck,
  ThreeDots,
} from 'react-bootstrap-icons';
import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import {
  Button,
  DropdownMenu,
  IconWrapper,
  Tooltip,
  Typography,
} from '@ghq-abi/design-system-v2';

import {
  DeliverableItem,
  DeliverableTypeEnum,
} from '~/shared/types/Deliverable';

export interface CatalogCardProps {
  data: DeliverableItem;
  isDragging?: boolean;
  isSelected?: boolean;
  disableDrag?: boolean;
  showActions?: boolean;
  showDetailAction?: boolean;
  showDrawerAction?: boolean;
  showOptionAction?: boolean;
  options?: {
    value: string;
    label: string;
    icon: React.ReactNode;
    onClick?: () => void;
  }[];
  showDefinition?: boolean;
  onDrawerActionClick?: () => void;
  onDetailActionClick?: () => void;
  onOptionActionClick?: () => void;
}

export function CatalogCard({
  data,
  isDragging = false,
  isSelected = false,
  disableDrag = false,
  showActions = false,
  showDetailAction = false,
  showOptionAction = false,
  showDefinition = false,
  showDrawerAction = false,
  onDrawerActionClick,
  onDetailActionClick,
  onOptionActionClick,
  options,
}: CatalogCardProps) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: dragging,
  } = useDraggable({
    id: data.uid,
    data: {
      type: 'deliverable',
      data,
    },
  });

  const style = {
    transform: CSS.Translate.toString(transform),
    opacity: dragging ? 0 : 1,
  };

  const isProject = data.type !== DeliverableTypeEnum.KPI;

  const icon = isProject ? <ListCheck /> : <BarChartFill />;
  const isActive = data.isActive;

  return (
    <div
      ref={!disableDrag ? setNodeRef : undefined}
      style={!disableDrag ? style : undefined}
      {...(!disableDrag ? listeners : {})}
      {...(!disableDrag ? attributes : {})}
      className={`
        relative bg-white border border-gray-200 rounded-lg p-4 min-h-[120px] transition-all duration-200
        ${!disableDrag ? 'cursor-grab' : ''}
        ${
          dragging || isDragging
            ? '!w-80  shadow-2xl z-[1000] cursor-grabbing transition-all duration-500 scale-75'
            : 'w-full'
        }
        ${isSelected ? 'opacity-60 scale-98' : ''}
      `}
    >
      <div className="flex items-start flex-col gap-3">
        {showActions && (
          <div className="flex w-full items-center gap-2 justify-end z-10">
            {showDrawerAction && (
              <Button
                variant="tertiary"
                size="icon"
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();
                  onDrawerActionClick?.();
                }}
                onPointerDown={e => e.stopPropagation()}
              >
                <ArrowUpRightSquare />
              </Button>
            )}
            {showDetailAction && (
              <Button
                variant="tertiary"
                size="icon"
                onClick={e => {
                  e.stopPropagation();
                  e.preventDefault();
                  onDetailActionClick?.();
                }}
                onPointerDown={e => e.stopPropagation()}
              >
                <ArrowsAngleExpand />
              </Button>
            )}
            {showOptionAction && (
              <DropdownMenu.Root>
                <DropdownMenu.Trigger asChild>
                  <Button
                    variant="tertiary"
                    size="icon"
                    onClick={e => {
                      e.stopPropagation();
                      e.preventDefault();
                      onOptionActionClick?.();
                    }}
                    onPointerDown={e => e.stopPropagation()}
                  >
                    <ThreeDots />
                  </Button>
                </DropdownMenu.Trigger>
                <DropdownMenu.Content className="w-48">
                  <DropdownMenu.RadioGroup
                    className="flex flex-col gap-2"
                    value={''}
                    onValueChange={() => {}}
                  >
                    {options?.map(option => (
                      <DropdownMenu.RadioItem
                        key={option.value}
                        value={option.value}
                        className="w-full flex items-center gap-2"
                        onClick={option.onClick}
                      >
                        <IconWrapper variant="default" round="none" size={26}>
                          {option.icon}
                        </IconWrapper>
                        <Typography variant="body-sm-regular">
                          {option.label}
                        </Typography>
                      </DropdownMenu.RadioItem>
                    ))}
                  </DropdownMenu.RadioGroup>
                </DropdownMenu.Content>
              </DropdownMenu.Root>
            )}
          </div>
        )}

        <div className="flex w-full justify-between">
          <div className="flex items-center gap-4">
            <div className="relative">
              <IconWrapper
                variant={isProject ? 'primary' : 'secondary'}
                round="md"
                size={32}
              >
                {icon}
              </IconWrapper>
              {!isActive && (
                <IconWrapper variant="disabled">
                  <EyeSlash
                    className="absolute bottom-0 right-0 left-4 bg-white rounded-full p-0.5"
                    style={{ transform: 'translate(30%, 30%)' }}
                    size={16}
                  />
                </IconWrapper>
              )}
            </div>
            <div className="flex flex-col">
              <Typography variant="body-sm-bold">{data.name}</Typography>
              <Typography variant="metadata-xs-regular">
                {data.businessFunction}
              </Typography>
            </div>
          </div>
        </div>

        {showDefinition && (
          <div className="w-full">
            <Tooltip.Provider>
              <Tooltip.Root>
                <Tooltip.Trigger>
                  <Typography
                    variant="body-sm-regular"
                    className="line-clamp-2 text-[#7D8597] text-start"
                  >
                    {data.definition}
                  </Typography>
                </Tooltip.Trigger>
                <Tooltip.Content
                  className="z-[9999] break-words max-w-sm"
                  side="bottom"
                  align="start"
                >
                  <Typography variant="body-sm-regular">
                    {data.definition}
                  </Typography>
                </Tooltip.Content>
              </Tooltip.Root>
            </Tooltip.Provider>
          </div>
        )}
      </div>
    </div>
  );
}
