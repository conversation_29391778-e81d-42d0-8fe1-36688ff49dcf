import { useEffect, useState } from 'react';

import { ProposalFilterOptions } from '../components/Proposal/types';

export const useProposalFilter = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [filters, setFilters] = useState<ProposalFilterOptions>({
    status: [],
    zones: [],
    businessFunctions: [],
    fuzzy_search: '',
    sltLevel: [],
    sltName: [],
  });

  const [selectedFunctions, setSelectedFunctions] = useState<string[]>(
    filters?.stagedBusinessFunctions ?? filters?.businessFunctions ?? [],
  );

  const [selectedStatus, setSelectedStatus] = useState<string[]>(
    filters?.stagedStatus ?? filters?.status ?? [],
  );

  const [selectedZones, setSelectedZones] = useState<string[]>([]);

  const [selectedSltLevel, setSelectedSltLevel] = useState<string[]>([]);

  const [selectedSltName, setSelectedSltName] = useState<string[]>([]);

  useEffect(() => {
    setSelectedFunctions(
      filters?.stagedBusinessFunctions ?? filters?.businessFunctions ?? [],
    );
  }, [filters?.stagedBusinessFunctions, filters?.businessFunctions]);

  const handleSearchChange = (query: string) => {
    setSearchQuery(query);
    if (!query.trim()) {
      setFilters({
        ...filters,
        fuzzy_search: '',
      });
    }
  };

  const handleSearchSubmit = () => {
    const nextBusinessFunctions =
      filters?.stagedBusinessFunctions ?? selectedFunctions ?? [];
    const nextStatus = filters?.stagedStatus ?? selectedStatus ?? [];
    const nextZones = filters?.stagedZones ?? selectedZones ?? [];
    const nextSltLevel = filters?.stagedSltLevel ?? selectedSltLevel ?? [];
    const nextSltName = filters?.stagedSltName ?? selectedSltName ?? [];

    setFilters({
      ...filters,
      fuzzy_search: searchQuery,
      businessFunctions: nextBusinessFunctions,
      stagedBusinessFunctions: nextBusinessFunctions,
      status: nextStatus,
      stagedStatus: nextStatus,
      zones: nextZones,
      stagedZones: nextZones,
      sltLevel: nextSltLevel,
      stagedSltLevel: nextSltLevel,
      sltName: nextSltName,
      stagedSltName: nextSltName,
    });
  };

  const handleSearchKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSearchSubmit();
    }
  };

  const handleToggle = (key: string, propName: string) => {
    if (propName === 'status') {
      setSelectedStatus(prev => {
        const next = prev.includes(key)
          ? prev.filter(f => f !== key)
          : [...prev, key];
        setFilters({
          ...filters,
          stagedStatus: next,
        });

        return next;
      });
    }

    if (propName === 'businessFunctions') {
      setSelectedFunctions(prev => {
        const next = prev.includes(key)
          ? prev.filter(f => f !== key)
          : [...prev, key];
        setFilters({
          ...filters,
          stagedBusinessFunctions: next,
        });

        return next;
      });
    }

    if (propName === 'zones') {
      setSelectedZones(prev => {
        const next = prev.includes(key)
          ? prev.filter(f => f !== key)
          : [...prev, key];
        setFilters({
          ...filters,
          stagedZones: next,
        });

        return next;
      });
    }

    if (propName === 'stlLevel') {
      setSelectedSltLevel(prev => {
        const next = prev.includes(key)
          ? prev.filter(f => f !== key)
          : [...prev, key];

        setFilters({
          ...filters,
          stagedSltLevel: next,
        });

        return next;
      });
    }

    if (propName === 'sltName') {
      setSelectedSltName(prev => {
        const next = prev.includes(key)
          ? prev.filter(f => f !== key)
          : [...prev, key];

        setFilters({
          ...filters,
          stagedSltName: next,
        });

        return next;
      });
    }
  };

  return {
    filters,
    setFilters,
    searchQuery,
    setSearchQuery,
    handleSearchChange,
    handleSearchKeyPress,
    handleSearchSubmit,
    handleToggle,
    selectedFunctions,
    selectedStatus,
    selectedZones,
    selectedSltLevel,
    selectedSltName,
  };
};
