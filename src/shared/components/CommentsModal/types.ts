export interface ProposalCommentData {
  id: string;
  author: {
    globalId: string;
    name: string;
  };
  message: string;
  createdAt: Date;
}

export interface CommentsModalRefApi {
  open: () => void;
  close: () => void;
  refresh: () => Promise<void>;
  loadProposalComments: () => Promise<void>;
}

export interface CommentsModalProps {
  parentId: string; // uuidv4
  title?: string;
  onClose?: () => void;
}
