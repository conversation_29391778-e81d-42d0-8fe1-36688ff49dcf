import { Target } from '~/shared/types/Target';
import { ProposalStatusEnum } from '~/shared/utils/enums';
import { TargetTypeEnum } from '~/shared/utils/enums/target-type';

export type TargetCardProps = {
  data: Target;
  proposalStatus?: ProposalStatusEnum;
  currentTargetType?: TargetTypeEnum;
  hideChildren?: boolean;
  deleteEvent?: (id: string) => void;
  onEditTarget?: (target: Target) => void;
  onOpenComments?: (target: Target) => void;
  onRemoveActionClick?: (target: Target[]) => void;
};
