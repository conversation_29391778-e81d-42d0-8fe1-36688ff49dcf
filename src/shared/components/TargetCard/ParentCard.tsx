import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'react-bootstrap-icons';
import {
  Card,
  Container,
  IconWrapper,
  Typography,
} from '@ghq-abi/design-system-v2';

import { Target } from '~/shared/types/Target';

import { Scale } from '../icons';

interface ParentCardProps {
  data: Target;
}

export function ParentCard({ data }: ParentCardProps) {
  return (
    <Card.Header className="flex flex-row gap-4 items-center pb-1">
      <IconWrapper variant="secondary" round="md" size={42}>
        <BarChartFill size={24} />
      </IconWrapper>
      <Card.Title className="flex flex-col">
        <Typography
          variant="body-md-regular"
          color="dark"
          className="font-semibold whitespace-nowrap overflow-hidden text-ellipsis"
        >
          {data.children?.map(child => child.deliverable?.name).join(' + ')}
        </Typography>
        <Container className="flex flex-row items-center gap-4">
          <Typography
            variant="body-sm-regular"
            color="dark"
            className="flex flex-row items-center gap-1"
          >
            <Person />
            {data.children
              ?.map(child => child.deliverable?.usage)
              .reduce((a, b) => (a || 0) + (b || 0), 0) || 0}
          </Typography>
          <Typography
            variant="body-sm-regular"
            color="dark"
            className="flex flex-row items-center gap-1"
          >
            <Scale />
            {data.children
              ?.map(child => child.weight)
              .reduce((a, b) => (a || 0) + (b || 0), 0) || 0}
            %
          </Typography>
        </Container>
      </Card.Title>
    </Card.Header>
  );
}
