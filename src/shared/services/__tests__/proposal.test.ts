import axios from 'axios';
import { ProposalService } from '../proposal';
import { ProposalFilters } from '../../types/ProposalService';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ProposalService', () => {
  let proposalService: ProposalService;

  beforeEach(() => {
    proposalService = new ProposalService();
    jest.clearAllMocks();
  });

  describe('getProposals', () => {
    it('should format sltName parameters correctly as separate query parameters', async () => {
      // Arrange
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const filters: ProposalFilters = {
        pageSize: 9,
        pageNumber: 1,
        sltName: ['5EF6EEAD-C90E-4B93-BF59-4E42F752C201', 'B3A38ACC-5F06-43F6-A99C-535C73358BAG'],
      };

      // Act
      await proposalService.getProposals(filters);

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);
      const calledUrl = mockedAxios.get.mock.calls[0][0];
      
      // Verify that sltName parameters are sent as separate query parameters
      expect(calledUrl).toContain('sltName=5EF6EEAD-C90E-4B93-BF59-4E42F752C201');
      expect(calledUrl).toContain('sltName=B3A38ACC-5F06-43F6-A99C-535C73358BAG');
      
      // Verify that sltName parameters are NOT comma-separated
      expect(calledUrl).not.toContain('sltName=5EF6EEAD-C90E-4B93-BF59-4E42F752C201%2CB3A38ACC-5F06-43F6-A99C-535C73358BAG');
      expect(calledUrl).not.toContain('sltName=5EF6EEAD-C90E-4B93-BF59-4E42F752C201,B3A38ACC-5F06-43F6-A99C-535C73358BAG');
    });

    it('should format all array parameters correctly as separate query parameters', async () => {
      // Arrange
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const filters: ProposalFilters = {
        pageSize: 9,
        pageNumber: 1,
        businessFunctions: ['func1', 'func2'],
        status: ['status1', 'status2'],
        zones: ['zone1', 'zone2'],
        sltLevel: ['level1', 'level2'],
        sltName: ['name1', 'name2'],
      };

      // Act
      await proposalService.getProposals(filters);

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);
      const calledUrl = mockedAxios.get.mock.calls[0][0];
      
      // Verify all array parameters are sent as separate query parameters
      expect(calledUrl).toContain('businessFunctions=func1');
      expect(calledUrl).toContain('businessFunctions=func2');
      expect(calledUrl).toContain('status=status1');
      expect(calledUrl).toContain('status=status2');
      expect(calledUrl).toContain('zones=zone1');
      expect(calledUrl).toContain('zones=zone2');
      expect(calledUrl).toContain('sltLevel=level1');
      expect(calledUrl).toContain('sltLevel=level2');
      expect(calledUrl).toContain('sltName=name1');
      expect(calledUrl).toContain('sltName=name2');
    });

    it('should handle single values in arrays correctly', async () => {
      // Arrange
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const filters: ProposalFilters = {
        pageSize: 9,
        pageNumber: 1,
        sltName: ['single-slt-name'],
      };

      // Act
      await proposalService.getProposals(filters);

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);
      const calledUrl = mockedAxios.get.mock.calls[0][0];
      
      expect(calledUrl).toContain('sltName=single-slt-name');
    });

    it('should not include empty array parameters in the URL', async () => {
      // Arrange
      const mockResponse = {
        data: {
          data: [],
          pageNumber: 1,
          pageSize: 9,
          totalRecords: 0,
        },
      };
      mockedAxios.get.mockResolvedValue(mockResponse);

      const filters: ProposalFilters = {
        pageSize: 9,
        pageNumber: 1,
        sltName: [],
      };

      // Act
      await proposalService.getProposals(filters);

      // Assert
      expect(mockedAxios.get).toHaveBeenCalledTimes(1);
      const calledUrl = mockedAxios.get.mock.calls[0][0];
      
      expect(calledUrl).not.toContain('sltName=');
    });
  });
});
