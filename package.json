{"name": "@ghq-abi/catchball-webclient", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint --max-warnings=0 --no-cache", "lint:fix": "npm run lint -- --fix", "prettier": "npx prettier src --check", "prettier:fix": "npm run prettier -- --write", "typecheck": "tsc --project tsconfig.json --noEmit", "test": "jest", "test:watch": "jest --watch", "test:ci": "jest --runInBand --ci --passWithNoTests"}, "engines": {"node": "20"}, "dependencies": {"@casl/ability": "6.5.0", "@casl/react": "3.1.0", "@datadog/browser-rum": "5.23.0", "@ghq-abi/auth-client-lib": "~2.1.4", "@ghq-abi/design-system": "0.0.41-59", "@ghq-abi/design-system-icons": "^0.0.81", "@ghq-abi/design-system-v2": "^1.0.12-beta.0", "@radix-ui/react-accordion": "1.0.0", "@tanstack/react-query": "4.29.5", "@tanstack/react-query-devtools": "^4.20.9", "@tolgee/format-icu": "5.12.0", "@tolgee/react": "5.10.2", "@types/lodash": "^4.17.20", "abi-opr-common-types": "2.4.190", "axios": "^1.8.3", "chalk": "^5.3.0", "clsx": "^2.1.1", "date-fns": "^2.30.0", "dd-trace": "4.29.0", "formik": "^2.4.6", "html2canvas": "^1.4.1", "lodash": "^4.17.21", "next": "13.5.9", "next-auth": "4.24.9", "nextjs-progressbar": "0.0.16", "nprogress": "0.2.0", "react": "18.2.0", "react-bootstrap-icons": "^1.11.6", "react-dnd": "^16.0.1", "react-dom": "18.2.0", "react-icons": "4.8.0", "react-infinite-scroll-component": "^6.1.0", "react-use": "^17.4.0", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "yup": "^1.6.1"}, "devDependencies": {"@axe-core/react": "4.7.0", "@tanstack/eslint-plugin-query": "4.29.4", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "13.4.0", "@testing-library/react-hooks": "8.0.1", "@types/html2canvas": "^0.5.35", "@types/node": "18.7.16", "@types/react": "18.0.18", "@types/react-dom": "18.0.6", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.21", "eslint": "^8", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.0.0", "eslint-config-next": "13.5.11", "eslint-config-prettier": "8.8.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-jest-dom": "4.0.3", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-simple-import-sort": "10.0.0", "eslint-plugin-testing-library": "5.10.2", "jest": "29.5.0", "jest-environment-jsdom": "29.5.0", "postcss": "^8.5.6", "prettier": "^2.8.8", "tailwindcss": "^3.4.17", "tsx": "3.12.7", "typescript": "5.0.4"}}